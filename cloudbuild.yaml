steps:
  # Setup buildx for both builds
  - name: "gcr.io/cloud-builders/docker"
    id: "setup-buildx"
    waitFor: ["-"]
    args:
      - "buildx"
      - "create"
      - "--name"
      - "builder"
      - "--use"
    env:
      - "DOCKER_BUILDKIT=1"

  # API pipeline with cache
  - name: "gcr.io/cloud-builders/docker"
    id: "build-api"
    waitFor: ["setup-buildx"]
    dir: "api"
    args:
      - "buildx"
      - "build"
      - "--builder"
      - "builder"
      - "--cache-from=type=registry,ref=us-central1-docker.pkg.dev/$PROJECT_ID/tora/api:cache"
      - "--cache-to=type=registry,ref=us-central1-docker.pkg.dev/$PROJECT_ID/tora/api:cache,mode=max"
      - "--push"
      - "-t"
      - "us-central1-docker.pkg.dev/$PROJECT_ID/tora/api:$COMMIT_SHA"
      - "-t"
      - "us-central1-docker.pkg.dev/$PROJECT_ID/tora/api:latest"
      - "."
    env:
      - "DOCKER_BUILDKIT=1"

  # Web pipeline with cache
  # - name: "gcr.io/cloud-builders/docker"
  #   id: "build-web"
  #   waitFor: ["setup-buildx"]
  #   dir: "web"
  #   args:
  #     - "buildx"
  #     - "build"
  #     - "--builder"
  #     - "builder"
  #     - "--cache-from=type=registry,ref=us-central1-docker.pkg.dev/$PROJECT_ID/tora/web:cache"
  #     - "--cache-to=type=registry,ref=us-central1-docker.pkg.dev/$PROJECT_ID/tora/web:cache,mode=max"
  #     - "--push"
  #     - "-t"
  #     - "us-central1-docker.pkg.dev/$PROJECT_ID/tora/web:$COMMIT_SHA"
  #     - "-t"
  #     - "us-central1-docker.pkg.dev/$PROJECT_ID/tora/web:latest"
  #     - "."
  #   env:
  #     - "DOCKER_BUILDKIT=1"

  # Deploy API (no longer needs separate push step)
  - name: "gcr.io/google.com/cloudsdktool/cloud-sdk"
    id: "deploy-api"
    waitFor: ["build-api"]
    dir: "api"
    entrypoint: "bash"
    args:
      - "-c"
      - |
        gcloud run deploy tora-api \
          --image us-central1-docker.pkg.dev/$PROJECT_ID/tora/api:$COMMIT_SHA \
          --region us-central1 \
          --platform managed \
          --allow-unauthenticated \
          --cpu 1.0 \
          --memory 512Mi \
          --min-instances 0 \
          --max-instances 5 \
          --set-secrets DATABASE_URL=DATABASE_URL:latest,SUPABASE_URL=SUPABASE_URL:latest,SUPABASE_API_KEY=SUPABASE_KEY:latest,SUPABASE_JWT_SECRET=SUPABASE_JWT_SECRET:latest \
          --set-env-vars FRONTEND_URL=https://tora-web.vercel.app,RUST_LOG=error

  # Deploy Web (no longer needs separate push step)
  # - name: "gcr.io/google.com/cloudsdktool/cloud-sdk"
  #   id: "deploy-web"
  #   waitFor: ["build-web"]
  #   dir: "web"
  #   entrypoint: "bash"
  #   args:
  #     - "-c"
  #     - |
  #       gcloud run deploy tora-web \
  #         --image us-central1-docker.pkg.dev/$PROJECT_ID/tora/web:$COMMIT_SHA \
  #         --region us-central1 \
  #         --platform managed \
  #         --allow-unauthenticated \
  #         --cpu 1.0 \
  #         --memory 512Mi \
  #         --min-instances 0 \
  #         --max-instances 5 \
  #         --set-env-vars PUBLIC_API_BASE_URL=https://tora-api-1030250455947.us-central1.run.app \
  #         --port 3000

timeout: "1600s"
logsBucket: "gs://artifacts.taigaishida-217622.appspot.com/logs"
options:
  machineType: "E2_HIGHCPU_8"
  logging: GCS_ONLY
  env:
    - "DOCKER_BUILDKIT=1"
