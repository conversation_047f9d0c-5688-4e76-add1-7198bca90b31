{"fileScopedDeclarationPrivacy": {"accessLevel": "private"}, "indentConditionalCompilationBlocks": true, "indentSwitchCaseLabels": false, "indentation": {"spaces": 4}, "lineBreakAroundMultilineExpressionChainComponents": false, "lineBreakBeforeControlFlowKeywords": false, "lineBreakBeforeEachArgument": false, "lineBreakBeforeEachGenericRequirement": false, "lineBreakBetweenDeclarationAttributes": false, "lineLength": 120, "maximumBlankLines": 1, "multiElementCollectionTrailingCommas": true, "noAssignmentInExpressions": {"allowedFunctions": ["XCTAssertNoThrow"]}, "prioritizeKeepingFunctionOutputTogether": false, "reflowMultilineStringLiterals": {"never": {}}, "respectsExistingLineBreaks": true, "rules": {"AllPublicDeclarationsHaveDocumentation": false, "AlwaysUseLiteralForEmptyCollectionInit": false, "AlwaysUseLowerCamelCase": true, "AmbiguousTrailingClosureOverload": true, "AvoidRetroactiveConformances": true, "BeginDocumentationCommentWithOneLineSummary": false, "DoNotUseSemicolons": true, "DontRepeatTypeInStaticProperties": true, "FileScopedDeclarationPrivacy": true, "FullyIndirectEnum": true, "GroupNumericLiterals": true, "IdentifiersMustBeASCII": true, "NeverForceUnwrap": false, "NeverUseForceTry": false, "NeverUseImplicitlyUnwrappedOptionals": false, "NoAccessLevelOnExtensionDeclaration": true, "NoAssignmentInExpressions": true, "NoBlockComments": true, "NoCasesWithOnlyFallthrough": true, "NoEmptyLinesOpeningClosingBraces": false, "NoEmptyTrailingClosureParentheses": true, "NoLabelsInCasePatterns": true, "NoLeadingUnderscores": false, "NoParensAroundConditions": true, "NoPlaygroundLiterals": true, "NoVoidReturnOnFunctionSignature": true, "OmitExplicitReturns": false, "OneCasePerLine": true, "OneVariableDeclarationPerLine": true, "OnlyOneTrailingClosureArgument": true, "OrderedImports": true, "ReplaceForEachWithForLoop": true, "ReturnVoidInsteadOfEmptyTuple": true, "TypeNamesShouldBeCapitalized": true, "UseEarlyExits": false, "UseExplicitNilCheckInConditions": true, "UseLetInEveryBoundCaseVariable": true, "UseShorthandTypeNames": true, "UseSingleLinePropertyGetter": true, "UseSynthesizedInitializer": true, "UseTripleSlashForDocumentationComments": true, "UseWhereClausesInForLoops": false, "ValidateDocumentationComments": false}, "spacesAroundRangeFormationOperators": false, "spacesBeforeEndOfLineComments": 2, "tabWidth": 4, "version": 1}