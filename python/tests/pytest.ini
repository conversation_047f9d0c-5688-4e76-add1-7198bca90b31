[tool:pytest]
# Pytest configuration for Tora SDK tests
# This ensures tests never accidentally hit production

# Test discovery
testpaths = tests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Environment isolation
env =
    TORA_API_KEY=test-api-key-pytest
    TORA_BASE_URL=https://test-pytest.example.com/api
    PYTHONPATH=.

# Markers
markers =
    unit: Unit tests that don't require external services
    integration: Integration tests that may require mocked services
    slow: Tests that take longer to run
    network: Tests that require network access (should be mocked)

# Test execution options
addopts =
    --strict-markers
    --strict-config
    --tb=short
    --cov=tora
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=80
    -v

# Coverage configuration
[coverage:run]
source = tora
omit =
    */tests/*
    */test_*
    */__pycache__/*
    */venv/*
    */env/*

[coverage:report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod
