[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "tora"
dynamic = ["version"]
description = "Python SDK for Tora ML experiment tracking platform"
readme = "README.md"
license = {file = "LICENSE"}
authors = [
    {name = "Tora Team"},
]
keywords = ["machine-learning", "experiment-tracking", "mlops", "data-science"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Typing :: Typed",
]
requires-python = ">=3.11"
dependencies = []

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-cov>=4.0.0",
    "pytest-asyncio>=0.21.0",
    "pytest-mock>=3.10.0",
    "pre-commit>=3.0.0",
    "twine>=4.0.0",
    "build>=0.10.0",
    "ruff>=0.1.0",
]
docs = [
    "mkdocs>=1.4.0",
    "mkdocs-material>=9.0.0",
    "mkdocstrings[python]>=0.20.0",
]

[project.urls]
Homepage = "https://pypi.org/project/tora/"
Repository = "https://github.com/taigaishida/tora"
Issues = "https://github.com/taigaishida/tora/issues"

# Development tool configurations
[tool.hatch.version]
path = "tora/__init__.py"

[tool.hatch.build.targets.wheel]
packages = ["tora"]

[tool.pytest.ini_options]
minversion = "7.0"
addopts = [
    "--strict-markers",
    "--strict-config",
    "--cov=tora",
    "--cov-report=term-missing",
    "--cov-report=html",
    "--cov-report=xml",
]
testpaths = ["tests"]
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "slow: Slow tests",
]

[tool.coverage.run]
source = ["tora"]
omit = [
    "*/tests/*",
    "*/test_*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]

[tool.ruff]
line-length = 120
target-version = "py311"

[tool.ruff.lint]
select = [
    "E",    # pycodestyle errors
    "W",    # pycodestyle warnings
    "F",    # pyflakes
    "I",    # isort
    "UP",   # pyupgrade
    "B",    # flake8-bugbear
    "C4",   # flake8-comprehensions
    "SIM",  # flake8-simplify
    "RUF",  # ruff-specific rules
]
ignore = [
    "E203",    # whitespace before ':'
]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401"]  # Unused imports in __init__.py
"tests/*" = ["D", "S101", "ANN"]  # No docstrings, allow asserts, no annotations in tests
"examples/*" = [
    "T201",    # Allow print statements in examples
    "S311",    # Allow pseudo-random generators in examples
    "BLE001",  # Allow catching Exception in examples
    "ANN",     # No type annotations required in examples
    "D401",    # Allow non-imperative docstrings in examples
    "EXE001",  # Allow shebang without executable in examples
]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

[dependency-groups]
dev = [
    "pytest>=8.4.1",
]
