[package]
name = "api"
version = "0.1.0"
edition = "2024"

[dependencies]
axum = { version = "0.8", features = ["json", "query"] }
axum-extra = { version = "0.10", features = ["cookie"] }
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
sqlx = { version = "0.8", features = [
  "postgres",
  "runtime-tokio-rustls",
  "macros",
  "chrono",
  "uuid",
] }
chrono = { version = "0.4", features = ["serde"] }
supabase-auth = "0.10.13"
tokio = { version = "1.45", features = [
  "rt-multi-thread", # the scheduler
  "macros",          # #[tokio::main]/#[tokio::test]
  "net",             # TcpStream/UnixStream for Postgres sockets
  "time",            # sleep(), timeouts used by sqlx’s pool
  "sync",            # channels, Mutex, Semaphore used by sqlx
  "signal",          # tokio::signal
] }
tower = "0.5"
tower-http = { version = "0.6", features = ["fs", "cors", "trace"] }
time = "0.3"
async-trait = "0.1"
uuid = { version = "1.0", features = ["v4"] }
sha2 = "0.10"
base64 = "0.22"
rand = "0.8"
http = "1.3.1"
hyper = { version = "1.0.0", features = ["full"] }
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
