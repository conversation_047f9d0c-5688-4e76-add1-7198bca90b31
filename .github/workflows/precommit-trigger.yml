name: Pre-commit Checks

on:
  workflow_call:
  workflow_dispatch:

jobs:
  precommit:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: "3.11"

    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: "20"

    - name: Install pnpm
      uses: pnpm/action-setup@v4
      with:
        version: 8

    - name: Set up Rust
      uses: dtolnay/rust-toolchain@stable
      with:
        components: rustfmt

    - name: Cache pre-commit
      uses: actions/cache@v4
      with:
        path: ~/.cache/pre-commit
        key: ${{ runner.os }}-precommit-${{ hashFiles('.pre-commit-config.yaml') }}
        restore-keys: |
          ${{ runner.os }}-precommit-

    - name: Cache pnpm dependencies
      uses: actions/cache@v4
      with:
        path: ~/.pnpm-store
        key: ${{ runner.os }}-pnpm-${{ hashFiles('web/pnpm-lock.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pnpm-

    - name: Cache Rust dependencies
      uses: actions/cache@v4
      with:
        path: |
          ~/.cargo/bin/
          ~/.cargo/registry/index/
          ~/.cargo/registry/cache/
          ~/.cargo/git/db/
          api/target/
        key: ${{ runner.os }}-cargo-${{ hashFiles('api/Cargo.lock') }}
        restore-keys: |
          ${{ runner.os }}-cargo-

    - name: Install Python dependencies
      working-directory: ./python
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev]"

    - name: Install web dependencies
      working-directory: ./web
      run: |
        pnpm install

    - name: Install pre-commit
      run: |
        pip install pre-commit

    - name: Run pre-commit
      run: |
        pre-commit run --all-files --show-diff-on-failure
