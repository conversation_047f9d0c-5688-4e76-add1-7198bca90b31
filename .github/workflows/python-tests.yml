name: Python SDK Tests

on:
  workflow_call:
  workflow_dispatch:

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.11", "3.12", "3.13"]

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Cache pip dependencies
      uses: actions/cache@v4
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ matrix.python-version }}-${{ hashFiles('python/pyproject.toml') }}
        restore-keys: |
          ${{ runner.os }}-pip-${{ matrix.python-version }}-

    - name: Install dependencies
      working-directory: ./python
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev]"

    - name: Verify environment
      working-directory: ./python
      run: |
        python --version
        python -c "import pytest; print(f'pytest version: {pytest.__version__}')"

    - name: Run tests
      working-directory: ./python
      run: |
        pytest -v

    - name: Upload coverage to Codecov
      if: matrix.python-version == '3.11'
      uses: codecov/codecov-action@v4
      with:
        file: ./python/coverage.xml
        flags: python
        name: python-coverage
        fail_ci_if_error: false
        token: ${{ secrets.CODECOV_TOKEN }}

    - name: Upload coverage artifacts
      if: matrix.python-version == '3.11'
      uses: actions/upload-artifact@v4
      with:
        name: coverage-report
        path: |
          python/coverage.xml
          python/htmlcov/
        retention-days: 30


  build-test:
    runs-on: ubuntu-latest
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v5
      with:
        python-version: "3.11"

    - name: Cache pip dependencies
      uses: actions/cache@v4
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-3.11-${{ hashFiles('python/pyproject.toml') }}
        restore-keys: |
          ${{ runner.os }}-pip-3.11-

    - name: Install dependencies
      working-directory: ./python
      run: |
        python -m pip install --upgrade pip
        pip install -e ".[dev]"

    - name: Build package
      working-directory: ./python
      run: |
        python -m build

    - name: Check build
      working-directory: ./python
      run: |
        twine check dist/*

    - name: Upload build artifacts
      uses: actions/upload-artifact@v4
      with:
        name: python-package
        path: python/dist/
        retention-days: 7
