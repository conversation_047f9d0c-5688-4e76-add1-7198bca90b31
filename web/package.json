{"name": "web", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "dev:hot": "../dev-hot.sh", "dev:frontend": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "build:production": "pnpm run check && pnpm run build", "format": "prettier --write .", "start": "node build"}, "devDependencies": {"@lucide/svelte": "^0.525.0", "@sveltejs/adapter-node": "^5.2.13", "@sveltejs/adapter-vercel": "^5.7.2", "@sveltejs/kit": "^2.22.5", "@sveltejs/vite-plugin-svelte": "^6.0.0", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.11", "chart.js": "^4.5.0", "marked": "^16.0.0", "prettier": "^3.6.2", "prettier-plugin-svelte": "^3.4.0", "shiki": "^3.7.0", "svelte": "^5.35.6", "svelte-check": "^4.2.2", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "vite": "^6.3.5"}, "pnpm": {"onlyBuiltDependencies": ["esbuild", "tailwindcss"]}}