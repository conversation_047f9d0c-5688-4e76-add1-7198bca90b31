services:
  api:
    build:
      context: ./api
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      - RUST_LOG=info
      - RUST_BACKTRACE=1
      - RUST_ENV=dev
      - DATABASE_URL=${DATABASE_URL}
      - SUPABASE_PASSWORD=${SUPABASE_PASSWORD}
      - SUPABASE_URL=${SUPABASE_URL}
      - SUPABASE_API_KEY=${SUPABASE_API_KEY}
      - SUPABASE_JWT_SECRET=${SUPABASE_JWT_SECRET}
      - FRONTEND_URL=http://web:3000
    networks:
      - tora-network
  web:
    build:
      context: ./web
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PUBLIC_API_BASE_URL=http://api:8080
      - ORIGIN=http://localhost:3000
    depends_on:
      - api
    networks:
      - tora-network
networks:
  tora-network:
    driver: bridge
