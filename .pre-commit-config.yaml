repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
        exclude: ^(web/|api/)
      - id: end-of-file-fixer
        exclude: ^(web/|api/)
      - id: check-yaml
        exclude: ^(web/|api/)
      - id: check-merge-conflict
      - id: check-toml
        files: \.(toml)$

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.9.0
    hooks:
      - id: ruff
        name: ruff-linting
        files: ^python/tora/
        args: ["--fix"]
      - id: ruff-format
        name: ruff-formatting
        files: ^python/tora/

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: debug-statements
        files: ^python/tora/

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.9.0
    hooks:
      - id: mypy
        files: ^python/tora/
        args:
          [
            "--ignore-missing-imports",
            "--no-strict-optional",
            "--allow-any-generics",
          ]
        additional_dependencies: [types-requests]

  - repo: local
    hooks:
      - id: prettier-web
        name: prettier (web)
        entry: pnpm --prefix web format
        language: system
        files: ^web/
        pass_filenames: false

  - repo: local
    hooks:
      - id: rustfmt-api
        name: rustfmt (api)
        entry: bash -c 'cd api && cargo fmt --all'
        language: system
        files: ^api/
        pass_filenames: false

  - repo: local
    hooks:
      - id: swift-format-ios
        name: swift-format (ios)
        entry: bash -c 'cd ios && swift-format format --in-place --recursive --configuration .swift-format.json .'
        language: system
        files: ^ios/.*\.swift$
        pass_filenames: false
